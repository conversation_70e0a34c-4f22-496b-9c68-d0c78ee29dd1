Logging started at 2025-07-29T10:48:53.885512543Z
[authlib-injector] [INFO] Version: 1.2.5
[authlib-injector] [INFO] Authentication server: https://littleskin.cn
[authlib-injector] [INFO] Redirect to: https://littleskin.cn/api/yggdrasil
[authlib-injector] [INFO] Transformed [com.mojang.authlib.properties.Property] with [Yggdrasil Public Key Transformer]
[authlib-injector] [INFO] Transformed [com.mojang.authlib.HttpAuthenticationService] with [ConcatenateURL Workaround]
[authlib-injector] [INFO] Httpd is running on port 39145
[authlib-injector] [INFO] Transformed [com.mojang.authlib.yggdrasil.YggdrasilEnvironment] with [Constant URL Transformer]
[authlib-injector] [INFO] Transformed [com.mojang.authlib.yggdrasil.YggdrasilServicesKeyInfo] with [Yggdrasil Public Key Transformer]
[authlib-injector] [INFO] Transformed [net.skinsrestorer.shared.connections.MojangAPIImpl] with [Constant URL Transformer]
[authlib-injector] [INFO] Transformed [net.minecraft.server.network.ServerLoginPacketListenerImpl] with [Username Character Check Transformer]
